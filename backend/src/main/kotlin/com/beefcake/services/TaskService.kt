package com.beefcake.services

import com.beefcake.database.tables.ActionType
import com.beefcake.database.tables.Priority
import com.beefcake.database.tables.TaskStatus
import com.beefcake.database.tables.TaskType
import com.beefcake.models.*
import com.beefcake.repositories.RequirementRepository
import com.beefcake.repositories.TaskDependencyRepository
import com.beefcake.repositories.TaskLogRepository
import com.beefcake.repositories.TaskRepository
import com.beefcake.repositories.UserRepository
import com.beefcake.utils.DateTimeUtils
import org.slf4j.LoggerFactory
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class TaskService {
    private val taskRepository = TaskRepository()
    private val taskDependencyRepository = TaskDependencyRepository()
    private val taskLogRepository = TaskLogRepository()
    private val userRepository = UserRepository()
    private val requirementRepository = RequirementRepository()
    private val logger = LoggerFactory.getLogger(TaskService::class.java)
    
    suspend fun createTask(request: TaskCreateRequest, creatorId: Long): Result<Task> {
        try {
            // 验证创建者存在
            val creator = userRepository.findById(creatorId)
                ?: return Result.failure(Exception("创建者不存在"))

            // 验证负责人存在（如果指定）
            if (request.assigneeId != null) {
                val assignee = userRepository.findById(request.assigneeId)
                    ?: return Result.failure(Exception("指定的负责人不存在"))
            }

            // 验证需求存在
            val requirement = requirementRepository.findById(request.requirementId)
                ?: return Result.failure(Exception("指定的需求不存在"))

            // 验证依赖任务存在
            if (request.dependencies.isNotEmpty()) {
                for (dependencyId in request.dependencies) {
                    val dependencyTask = taskRepository.findById(dependencyId)
                        ?: return Result.failure(Exception("依赖任务 $dependencyId 不存在"))

                    // 验证依赖任务属于同一需求
                    if (dependencyTask.requirementId != request.requirementId) {
                        return Result.failure(Exception("依赖任务必须属于同一需求"))
                    }
                }
            }

            // 解析截止时间
            val dueDate = request.dueDate?.let {
                try {
                    DateTimeUtils.parseDateTime(it)
                } catch (e: Exception) {
                    return Result.failure(Exception("截止时间格式错误: ${e.message}"))
                }
            }
            
            // 创建任务
            val task = taskRepository.create(
                title = request.title,
                description = request.description,
                requirementId = request.requirementId,
                taskType = request.taskType,
                priorityImportance = request.priorityImportance,
                priorityUrgency = request.priorityUrgency,
                estimatedHours = request.estimatedHours,
                creatorId = creatorId,
                assigneeId = request.assigneeId,
                dueDate = dueDate
            ) ?: return Result.failure(Exception("任务创建失败"))
            
            // 记录操作日志
            taskLogRepository.create(
                taskId = task.id,
                userId = creatorId,
                actionType = ActionType.CREATE,
                comment = "创建任务"
            )
            
            // 如果指定了负责人，记录分配日志
            if (request.assigneeId != null) {
                taskLogRepository.create(
                    taskId = task.id,
                    userId = creatorId,
                    actionType = ActionType.UPDATE_ASSIGNEE,
                    newValue = request.assigneeId.toString(),
                    comment = "分配任务"
                )
            }

            // 创建任务依赖关系
            if (request.dependencies.isNotEmpty()) {
                val createdDependencies = taskDependencyRepository.createDependencies(
                    taskId = task.id,
                    dependsOnTaskIds = request.dependencies
                )

                if (createdDependencies.size != request.dependencies.size) {
                    logger.warn("部分依赖关系创建失败，任务ID: ${task.id}")
                }

                // 记录依赖关系创建日志
                taskLogRepository.create(
                    taskId = task.id,
                    userId = creatorId,
                    actionType = ActionType.CREATE,
                    newValue = "依赖任务: ${request.dependencies.joinToString(", ")}",
                    comment = "创建任务依赖关系"
                )
            }

            logger.info("用户 $creatorId 创建了任务: ${task.title}")
            return Result.success(task)
            
        } catch (e: Exception) {
            logger.error("创建任务失败", e)
            return Result.failure(e)
        }
    }
    
    suspend fun getTaskById(id: Long): Task? {
        return taskRepository.findByIdWithDependencies(id)
    }
    
    suspend fun updateTask(id: Long, request: TaskUpdateRequest, userId: Long): Result<Task> {
        try {
            // 验证任务存在
            val existingTask = taskRepository.findById(id)
                ?: return Result.failure(Exception("任务不存在"))
            
            // 验证权限（创建者或负责人可以修改）
            if (existingTask.creatorId != userId && existingTask.assigneeId != userId) {
                return Result.failure(Exception("权限不足"))
            }
            
            // 验证负责人存在（如果要修改）
            if (request.assigneeId != null) {
                val assignee = userRepository.findById(request.assigneeId)
                    ?: return Result.failure(Exception("指定的负责人不存在"))
            }
            
            // 解析截止时间
            val dueDate = request.dueDate?.let {
                try {
                    DateTimeUtils.parseDateTime(it)
                } catch (e: Exception) {
                    return Result.failure(Exception("截止时间格式错误: ${e.message}"))
                }
            }
            
            // 记录变更日志
            val changes = mutableListOf<String>()
            
            if (request.title != null && request.title != existingTask.title) {
                changes.add("标题: ${existingTask.title} -> ${request.title}")
            }
            if (request.status != null && request.status != existingTask.status) {
                changes.add("状态: ${existingTask.status} -> ${request.status}")
            }
            if (request.assigneeId != existingTask.assigneeId) {
                changes.add("负责人: ${existingTask.assigneeId} -> ${request.assigneeId}")
            }
            if (request.priorityImportance != null && request.priorityImportance != existingTask.priorityImportance) {
                changes.add("重要程度: ${existingTask.priorityImportance} -> ${request.priorityImportance}")
            }
            if (request.priorityUrgency != null && request.priorityUrgency != existingTask.priorityUrgency) {
                changes.add("紧急程度: ${existingTask.priorityUrgency} -> ${request.priorityUrgency}")
            }
            
            // 更新任务
            val success = taskRepository.update(
                id = id,
                title = request.title,
                description = request.description,
                taskType = request.taskType,
                status = request.status,
                priorityImportance = request.priorityImportance,
                priorityUrgency = request.priorityUrgency,
                estimatedHours = request.estimatedHours,
                actualHours = request.actualHours,
                assigneeId = request.assigneeId,
                dueDate = dueDate
            )
            
            if (!success) {
                return Result.failure(Exception("任务更新失败"))
            }
            
            // 记录操作日志
            if (changes.isNotEmpty()) {
                taskLogRepository.create(
                    taskId = id,
                    userId = userId,
                    actionType = when {
                        request.status != null -> ActionType.UPDATE_STATUS
                        request.assigneeId != null -> ActionType.UPDATE_ASSIGNEE
                        request.priorityImportance != null || request.priorityUrgency != null -> ActionType.UPDATE_PRIORITY
                        else -> ActionType.CREATE
                    },
                    oldValue = existingTask.toString(),
                    newValue = changes.joinToString("; "),
                    comment = "更新任务"
                )
            }
            
            val updatedTask = taskRepository.findById(id)!!
            logger.info("用户 $userId 更新了任务 $id")
            return Result.success(updatedTask)
            
        } catch (e: Exception) {
            logger.error("更新任务失败", e)
            return Result.failure(e)
        }
    }
    
    suspend fun updateTaskStatus(id: Long, status: TaskStatus, userId: Long, comment: String? = null): Result<Task> {
        try {
            val existingTask = taskRepository.findById(id)
                ?: return Result.failure(Exception("任务不存在"))
            
            // 验证权限
            if (existingTask.creatorId != userId && existingTask.assigneeId != userId) {
                return Result.failure(Exception("权限不足"))
            }
            
            val success = taskRepository.updateStatus(id, status)
            if (!success) {
                return Result.failure(Exception("状态更新失败"))
            }
            
            // 记录操作日志
            taskLogRepository.create(
                taskId = id,
                userId = userId,
                actionType = ActionType.UPDATE_STATUS,
                oldValue = existingTask.status.toString(),
                newValue = status.toString(),
                comment = comment ?: "更新任务状态"
            )
            
            val updatedTask = taskRepository.findById(id)!!
            logger.info("用户 $userId 将任务 $id 状态更新为 $status")
            return Result.success(updatedTask)
            
        } catch (e: Exception) {
            logger.error("更新任务状态失败", e)
            return Result.failure(e)
        }
    }
    
    suspend fun deleteTask(id: Long, userId: Long): Result<Unit> {
        try {
            val task = taskRepository.findById(id)
                ?: return Result.failure(Exception("任务不存在"))
            
            // 只有创建者可以删除任务
            if (task.creatorId != userId) {
                return Result.failure(Exception("只有任务创建者可以删除任务"))
            }
            
            val success = taskRepository.delete(id)
            if (!success) {
                return Result.failure(Exception("任务删除失败"))
            }
            
            logger.info("用户 $userId 删除了任务 $id")
            return Result.success(Unit)
            
        } catch (e: Exception) {
            logger.error("删除任务失败", e)
            return Result.failure(e)
        }
    }
    
    suspend fun getTaskList(params: TaskQueryParams): TaskListResponse {
        val (tasks, total) = taskRepository.findAllWithRelations(params)
        return TaskListResponse(tasks, total, params.page, params.pageSize)
    }
    
    suspend fun getKanbanData(): KanbanData {
        return KanbanData(
            todo = taskRepository.findByStatus(TaskStatus.TODO),
            inProgress = taskRepository.findByStatus(TaskStatus.IN_PROGRESS),
            blocked = taskRepository.findByStatus(TaskStatus.BLOCKED),
            review = taskRepository.findByStatus(TaskStatus.REVIEW),
            testing = taskRepository.findByStatus(TaskStatus.TESTING),
            done = taskRepository.findByStatus(TaskStatus.DONE)
        )
    }
    
    suspend fun getQuadrantData(): QuadrantData {
        val allTasks = taskRepository.findAll(TaskQueryParams(pageSize = 1000)).first
        
        return QuadrantData(
            urgentImportant = allTasks.filter { 
                it.priorityUrgency == Priority.HIGH && it.priorityImportance == Priority.HIGH 
            },
            notUrgentImportant = allTasks.filter { 
                it.priorityUrgency != Priority.HIGH && it.priorityImportance == Priority.HIGH 
            },
            urgentNotImportant = allTasks.filter { 
                it.priorityUrgency == Priority.HIGH && it.priorityImportance != Priority.HIGH 
            },
            notUrgentNotImportant = allTasks.filter { 
                it.priorityUrgency != Priority.HIGH && it.priorityImportance != Priority.HIGH 
            }
        )
    }
    
    suspend fun getTaskStatistics(): TaskStatistics {
        val stats = taskRepository.getTaskStatistics()
        
        return TaskStatistics(
            totalTasks = stats["totalTasks"] as Int,
            completedTasks = stats["completedTasks"] as Int,
            inProgressTasks = stats["inProgressTasks"] as Int,
            overdueTasks = stats["overdueTasks"] as Int,
            tasksByStatus = emptyMap(), // TODO: 实现详细统计
            tasksByPriority = emptyMap(), // TODO: 实现详细统计
            averageCompletionTime = null, // TODO: 实现平均完成时间计算
            totalEstimatedHours = 0.0, // TODO: 实现工时统计
            totalActualHours = 0.0 // TODO: 实现工时统计
        )
    }
    
    suspend fun getTaskLogs(taskId: Long): List<TaskLog> {
        return taskLogRepository.findByTaskId(taskId)
    }

    suspend fun getUpstreamTaskOptions(requirementId: Long): List<TaskOption> {
        val tasks = taskRepository.findByRequirementId(requirementId)

        return tasks.map { task ->
            TaskOption(
                id = task.id,
                title = task.title,
                status = task.status.toString(),
                taskType = task.taskType.toString()
            )
        }
    }
}
