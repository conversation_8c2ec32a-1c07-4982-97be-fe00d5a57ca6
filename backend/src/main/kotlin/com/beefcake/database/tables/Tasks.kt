package com.beefcake.database.tables

import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.*
import java.time.LocalDateTime

object Tasks : LongIdTable("tasks") {
    val title = varchar("title", 200)
    val description = text("description").nullable() // 技术描述
    val requirementId = long("requirement_id").references(Requirements.id) // 关联需求
    val taskType = enumerationByName("task_type", 20, TaskType::class).default(TaskType.DEVELOPMENT)
    val statusId = long("status_id").references(TaskStatus.id)
    val priorityImportance = enumerationByName("priority_importance", 20, Priority::class).default(Priority.MEDIUM)
    val priorityUrgency = enumerationByName("priority_urgency", 20, Priority::class).default(Priority.MEDIUM)
    val estimatedHours = decimal("estimated_hours", 5, 2).nullable()
    val actualHours = decimal("actual_hours", 5, 2).nullable()
    val creatorId = long("creator_id").references(Users.id)
    val assigneeId = long("assignee_id").references(Users.id).nullable()
    val dueDate = datetime("due_date").nullable()
    val startedAt = datetime("started_at").nullable()
    val completedAt = datetime("completed_at").nullable()
    val createdAt = datetime("created_at")
    val updatedAt = datetime("updated_at")
}

// TaskStatus 现在使用数据库表，不再使用枚举

enum class TaskType {
    DESIGN,         // 设计任务（UI/UX）
    DEVELOPMENT,    // 开发任务
    TESTING,        // 测试任务
    DEPLOYMENT,     // 部署任务
    DOCUMENTATION,  // 文档任务
    RESEARCH        // 调研任务
}

enum class Priority {
    HIGH, MEDIUM, LOW
}

// 任务依赖关系表
object TaskDependencies : LongIdTable("task_dependencies") {
    val taskId = long("task_id").references(Tasks.id) // 当前任务
    val dependsOnTaskId = long("depends_on_task_id").references(Tasks.id) // 依赖的任务
    val dependencyType =
        enumerationByName("dependency_type", 20, DependencyType::class).default(DependencyType.FINISH_TO_START)
    val createdAt = datetime("created_at")

    init {
        // 确保同一对任务只能有一个依赖关系
        uniqueIndex(taskId, dependsOnTaskId)
    }
}

enum class DependencyType {
    FINISH_TO_START,    // 前置任务完成后，当前任务才能开始（最常见）
    START_TO_START,     // 前置任务开始后，当前任务才能开始（并行任务）
    FINISH_TO_FINISH,   // 前置任务完成后，当前任务才能完成
    START_TO_FINISH     // 前置任务开始后，当前任务才能完成
}
