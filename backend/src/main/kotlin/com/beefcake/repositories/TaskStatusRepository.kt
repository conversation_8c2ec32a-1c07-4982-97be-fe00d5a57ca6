package com.beefcake.repositories

import com.beefcake.database.DatabaseFactory.dbQuery
import com.beefcake.database.tables.TaskStatuses
import com.beefcake.models.TaskStatusModel
import com.beefcake.models.TaskStatusCreateRequest
import com.beefcake.models.TaskStatusUpdateRequest
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class TaskStatusRepository {

    /**
     * 获取所有任务状态（按排序顺序）
     */
    suspend fun findAll(includeInactive: Boolean = false): List<TaskStatusModel> = dbQuery {
        var query = TaskStatuses.selectAll()

        if (!includeInactive) {
            query = query.andWhere { TaskStatuses.isActive eq true }
        }

        query.orderBy(TaskStatuses.sortOrder, SortOrder.ASC)
            .map { resultRowToTaskStatus(it) }
    }

    /**
     * 根据ID获取任务状态
     */
    suspend fun findById(id: Long): TaskStatusModel? = dbQuery {
        TaskStatuses.selectAll().where { TaskStatuses.id eq id }
            .map { resultRowToTaskStatus(it) }
            .singleOrNull()
    }

    /**
     * 根据名称获取任务状态
     */
    suspend fun findByName(name: String): TaskStatusModel? = dbQuery {
        TaskStatuses.selectAll().where { TaskStatuses.name eq name }
            .map { resultRowToTaskStatus(it) }
            .singleOrNull()
    }

    /**
     * 创建任务状态
     */
    suspend fun create(request: TaskStatusCreateRequest): TaskStatusModel? = dbQuery {
        val now = LocalDateTime.now()

        // 获取下一个排序顺序
        val maxSortOrder = TaskStatuses.selectAll()
            .maxByOrNull { it[TaskStatuses.sortOrder] }
            ?.get(TaskStatuses.sortOrder) ?: 0

        val insertStatement = TaskStatuses.insert {
            it[name] = request.name
            it[displayName] = request.displayName
            it[description] = request.description
            it[color] = request.color
            it[sortOrder] = maxSortOrder + 1
            it[isActive] = true
            it[isSystem] = false
            it[createdAt] = now
            it[updatedAt] = now
        }

        insertStatement.resultedValues?.singleOrNull()?.let { resultRowToTaskStatus(it) }
    }

    /**
     * 更新任务状态
     */
    suspend fun update(id: Long, request: TaskStatusUpdateRequest): Boolean = dbQuery {
        val now = LocalDateTime.now()

        TaskStatuses.update({ TaskStatuses.id eq id }) {
            it[TaskStatuses.displayName] = request.displayName
            it[TaskStatuses.description] = request.description
            it[TaskStatuses.color] = request.color
            it[TaskStatuses.isActive] = request.isActive
            it[TaskStatuses.updatedAt] = now
        } > 0
    }

    /**
     * 删除任务状态（仅非系统状态可删除）
     */
    suspend fun delete(id: Long): Boolean = dbQuery {
        TaskStatuses.deleteWhere {
            (TaskStatuses.id eq id) and (TaskStatuses.isSystem eq false)
        } > 0
    }

    /**
     * 更新排序顺序
     */
    suspend fun updateSortOrder(statusIds: List<Long>): Boolean = dbQuery {
        var success = true
        statusIds.forEachIndexed { index, statusId ->
            val updated = TaskStatuses.update({ TaskStatuses.id eq statusId }) {
                it[sortOrder] = index + 1
                it[updatedAt] = LocalDateTime.now()
            }
            if (updated == 0) {
                success = false
            }
        }
        success
    }

    /**
     * 检查状态是否被任务使用
     */
    suspend fun isStatusInUse(@Suppress("UNUSED_PARAMETER") statusName: String): Boolean = dbQuery {
        // 这里需要检查Tasks表中是否有使用该状态的任务
        // 由于当前Tasks表使用枚举，这里先返回false
        // 后续如果改为引用TaskStatuses表，需要修改此逻辑
        false
    }

    /**
     * 将ResultRow转换为TaskStatusModel
     */
    private fun resultRowToTaskStatus(row: ResultRow): TaskStatusModel {
        val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
        return TaskStatusModel(
            id = row[TaskStatuses.id].value,
            name = row[TaskStatuses.name],
            displayName = row[TaskStatuses.displayName],
            description = row[TaskStatuses.description],
            color = row[TaskStatuses.color],
            sortOrder = row[TaskStatuses.sortOrder],
            isActive = row[TaskStatuses.isActive],
            isSystem = row[TaskStatuses.isSystem],
            createdAt = row[TaskStatuses.createdAt].format(formatter),
            updatedAt = row[TaskStatuses.updatedAt].format(formatter)
        )
    }
}
