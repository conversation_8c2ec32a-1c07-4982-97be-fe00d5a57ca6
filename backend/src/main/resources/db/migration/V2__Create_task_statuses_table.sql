-- 创建任务状态表
CREATE TABLE task_status (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    color VARCHAR(20) NOT NULL DEFAULT '#d9d9d9',
    sort_order INT NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_system BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
);

-- 插入系统默认状态
INSERT INTO task_status (name, display_name, description, color, sort_order, is_active, is_system, created_at, updated_at) VALUES
('TODO', '待办', '任务已创建，等待开始执行', '#d9d9d9', 1, TRUE, TRUE, NOW(), NOW()),
('IN_PROGRESS', '进行中', '任务正在执行中', '#1890ff', 2, TRUE, TRUE, NOW(), NOW()),
('BLOCKED', '阻塞', '任务被阻塞，无法继续进行', '#ff4d4f', 3, TRUE, TRUE, NOW(), NOW()),
('REVIEW', '评审', '任务已完成，等待评审', '#fa8c16', 4, TRUE, TRUE, NOW(), NOW()),
('TESTING', '测试', '任务正在测试中', '#722ed1', 5, TRUE, TRUE, NOW(), NOW()),
('DONE', '完成', '任务已完成并通过验收', '#52c41a', 6, TRUE, TRUE, NOW(), NOW());
