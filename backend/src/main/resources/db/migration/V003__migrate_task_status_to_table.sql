-- 迁移任务状态从枚举到表结构
-- V003__migrate_task_status_to_table.sql

-- 1. 创建任务状态表
CREATE TABLE IF NOT EXISTS task_status (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    color VARCHAR(20) DEFAULT '#d9d9d9',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    is_system BOOLEAN DEFAULT FALSE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    INDEX idx_task_status_sort_order (sort_order),
    INDEX idx_task_status_active (is_active)
);

-- 2. 插入默认任务状态
INSERT INTO task_status (name, display_name, description, color, sort_order, is_active, is_system, created_at, updated_at) VALUES
('TODO', '待开始', '任务已创建，等待开始执行', '#d9d9d9', 1, TRUE, TRUE, NOW(), NOW()),
('IN_PROGRESS', '进行中', '任务正在执行中', '#1890ff', 2, TRUE, TRUE, NOW(), NOW()),
('BLOCKED', '阻塞', '任务被阻塞，等待依赖任务完成或问题解决', '#faad14', 3, TRUE, TRUE, NOW(), NOW()),
('REVIEW', '评审中', '任务已完成，等待评审', '#722ed1', 4, TRUE, TRUE, NOW(), NOW()),
('TESTING', '测试中', '任务正在测试中', '#13c2c2', 5, TRUE, TRUE, NOW(), NOW()),
('DONE', '已完成', '任务已完成并通过验收', '#52c41a', 6, TRUE, TRUE, NOW(), NOW());

-- 3. 添加新的status_id列到tasks表
ALTER TABLE tasks ADD COLUMN status_id BIGINT;

-- 4. 创建外键约束
ALTER TABLE tasks ADD CONSTRAINT fk_tasks_status_id 
    FOREIGN KEY (status_id) REFERENCES task_status(id);

-- 5. 迁移现有数据：将枚举状态映射到状态表ID
UPDATE tasks t 
SET status_id = (
    SELECT ts.id 
    FROM task_status ts 
    WHERE ts.name = t.status
)
WHERE t.status IS NOT NULL;

-- 6. 设置默认状态为TODO（对于没有状态的任务）
UPDATE tasks t 
SET status_id = (
    SELECT ts.id 
    FROM task_status ts 
    WHERE ts.name = 'TODO'
)
WHERE t.status_id IS NULL;

-- 7. 将status_id设为非空
ALTER TABLE tasks MODIFY COLUMN status_id BIGINT NOT NULL;

-- 8. 删除旧的status列（注意：这是不可逆操作）
-- ALTER TABLE tasks DROP COLUMN status;

-- 注意：暂时保留旧的status列，以便在确认迁移成功后手动删除
-- 可以在确认新系统工作正常后执行：
-- ALTER TABLE tasks DROP COLUMN status;
