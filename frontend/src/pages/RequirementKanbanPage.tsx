import React, { useState } from 'react'
import {
  Typography,
  Button,
  Space,
  message,
  Modal,
  Statistic,
  Row,
  Col,
  Card,
  Tooltip,
  Spin,
  Alert
} from 'antd'
import {
  PlusOutlined,
  ReloadOutlined,
  BarChartOutlined,
  EyeOutlined,
  EditOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { requirementApi } from '../services/api'
import type { 
  Requirement, 
  RequirementStatus, 
  RequirementKanbanData,
  RequirementUpdateRequest 
} from '../types'
import RequirementKanbanBoard from '../components/RequirementKanbanBoard'

const { Title, Text } = Typography

const RequirementKanbanPage: React.FC = () => {
  const queryClient = useQueryClient()
  const [selectedRequirement, setSelectedRequirement] = useState<Requirement | null>(null)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)

  // 获取需求看板数据
  const { data: kanbanData, isLoading, error } = useQuery({
    queryKey: ['requirement-kanban'],
    queryFn: requirementApi.getRequirementKanbanData,
    refetchInterval: 30000, // 30秒自动刷新
  })

  // 更新需求状态
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: number; status: RequirementStatus }) =>
      requirementApi.updateRequirement(id, { status }),
    onSuccess: () => {
      message.success('需求状态更新成功')
      queryClient.invalidateQueries({ queryKey: ['requirement-kanban'] })
      queryClient.invalidateQueries({ queryKey: ['requirements'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '状态更新失败')
    },
  })

  // 处理需求拖拽移动
  const handleRequirementMove = (requirementId: number, newStatus: RequirementStatus) => {
    updateStatusMutation.mutate({ id: requirementId, status: newStatus })
  }

  // 处理需求点击
  const handleRequirementClick = (requirement: Requirement) => {
    setSelectedRequirement(requirement)
    setIsDetailModalVisible(true)
  }

  // 刷新数据
  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ['requirement-kanban'] })
    message.success('数据已刷新')
  }

  // 计算统计数据
  const getStatistics = (data: RequirementKanbanData | undefined) => {
    if (!data) return { total: 0, inProgress: 0, completed: 0, overdue: 0 }
    
    const total = Object.values(data).flat().length
    const inProgress = data.inProgress.length + data.testing.length
    const completed = data.delivered.length
    
    // 计算逾期需求
    const now = new Date()
    const overdue = Object.values(data).flat().filter(req => 
      req.expectedDeliveryDate && 
      new Date(req.expectedDeliveryDate) < now &&
      req.status !== 'DELIVERED'
    ).length

    return { total, inProgress, completed, overdue }
  }

  const statistics = getStatistics(kanbanData)

  if (error) {
    return (
      <div style={{ padding: 24 }}>
        <Alert
          message="加载失败"
          description="无法加载需求看板数据，请检查网络连接或稍后重试。"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={handleRefresh}>
              重试
            </Button>
          }
        />
      </div>
    )
  }

  return (
    <div style={{ padding: 24 }}>
      {/* 页面标题和操作 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div>
            <Title level={2} style={{ margin: 0 }}>
              需求看板
            </Title>
            <Text type="secondary">
              可视化管理需求流程，拖拽卡片更新状态
            </Text>
          </div>
          <Space>
            <Tooltip title="刷新数据">
              <Button 
                icon={<ReloadOutlined />} 
                onClick={handleRefresh}
                loading={isLoading}
              />
            </Tooltip>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => {
                // 跳转到需求创建页面
                window.location.href = '/requirements'
              }}
            >
              创建需求
            </Button>
          </Space>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="总需求"
              value={statistics.total}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="进行中"
              value={statistics.inProgress}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="已完成"
              value={statistics.completed}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="逾期"
              value={statistics.overdue}
              valueStyle={{ color: statistics.overdue > 0 ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 使用提示 */}
      <Alert
        message="使用提示"
        description="拖拽需求卡片到不同列可以更新需求状态。点击卡片查看详细信息。"
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
        style={{ marginBottom: 16 }}
        closable
      />

      {/* 看板 */}
      <RequirementKanbanBoard
        data={kanbanData}
        loading={isLoading}
        onRequirementClick={handleRequirementClick}
        onRequirementMove={handleRequirementMove}
      />

      {/* 需求详情模态框 */}
      <Modal
        title={
          <Space>
            <EyeOutlined />
            需求详情
          </Space>
        }
        open={isDetailModalVisible}
        onCancel={() => {
          setIsDetailModalVisible(false)
          setSelectedRequirement(null)
        }}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>,
          <Button 
            key="edit" 
            type="primary" 
            icon={<EditOutlined />}
            onClick={() => {
              // 跳转到需求编辑页面
              if (selectedRequirement) {
                window.location.href = `/requirements?edit=${selectedRequirement.id}`
              }
            }}
          >
            编辑需求
          </Button>
        ]}
        width={800}
      >
        {selectedRequirement && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Card size="small" title="基本信息">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Text strong>需求ID: </Text>
                      <Text>#{selectedRequirement.id}</Text>
                    </Col>
                    <Col span={12}>
                      <Text strong>状态: </Text>
                      <Text>{selectedRequirement.status}</Text>
                    </Col>
                  </Row>
                  <Row gutter={16} style={{ marginTop: 8 }}>
                    <Col span={24}>
                      <Text strong>标题: </Text>
                      <Text>{selectedRequirement.title}</Text>
                    </Col>
                  </Row>
                  <Row gutter={16} style={{ marginTop: 8 }}>
                    <Col span={24}>
                      <Text strong>业务描述: </Text>
                      <Text>{selectedRequirement.businessDescription}</Text>
                    </Col>
                  </Row>
                </Card>
              </Col>
              
              {selectedRequirement.acceptanceCriteria && (
                <Col span={24}>
                  <Card size="small" title="验收标准">
                    <Text>{selectedRequirement.acceptanceCriteria}</Text>
                  </Card>
                </Col>
              )}
              
              <Col span={12}>
                <Card size="small" title="优先级">
                  <Row gutter={8}>
                    <Col span={12}>
                      <Text strong>重要程度: </Text>
                      <Text>{selectedRequirement.priorityImportance}</Text>
                    </Col>
                    <Col span={12}>
                      <Text strong>紧急程度: </Text>
                      <Text>{selectedRequirement.priorityUrgency}</Text>
                    </Col>
                  </Row>
                </Card>
              </Col>
              
              <Col span={12}>
                <Card size="small" title="业务信息">
                  {selectedRequirement.estimatedValue && (
                    <div style={{ marginBottom: 4 }}>
                      <Text strong>预估价值: </Text>
                      <Text>{selectedRequirement.estimatedValue}</Text>
                    </div>
                  )}
                  {selectedRequirement.targetUsers && (
                    <div style={{ marginBottom: 4 }}>
                      <Text strong>目标用户: </Text>
                      <Text>{selectedRequirement.targetUsers}</Text>
                    </div>
                  )}
                </Card>
              </Col>
              
              {selectedRequirement.businessGoal && (
                <Col span={24}>
                  <Card size="small" title="业务目标">
                    <Text>{selectedRequirement.businessGoal}</Text>
                  </Card>
                </Col>
              )}
              
              <Col span={24}>
                <Card size="small" title="时间信息">
                  <Row gutter={16}>
                    <Col span={8}>
                      <Text strong>创建时间: </Text>
                      <Text>{selectedRequirement.createdAt}</Text>
                    </Col>
                    <Col span={8}>
                      <Text strong>期望交付: </Text>
                      <Text>{selectedRequirement.expectedDeliveryDate || '未设置'}</Text>
                    </Col>
                    <Col span={8}>
                      <Text strong>实际交付: </Text>
                      <Text>{selectedRequirement.actualDeliveryDate || '未完成'}</Text>
                    </Col>
                  </Row>
                </Card>
              </Col>
              
              {selectedRequirement.tasks && selectedRequirement.tasks.length > 0 && (
                <Col span={24}>
                  <Card size="small" title={`关联任务 (${selectedRequirement.tasks.length})`}>
                    {selectedRequirement.tasks.map(task => (
                      <div key={task.id} style={{ 
                        padding: 8, 
                        border: '1px solid #f0f0f0', 
                        borderRadius: 4, 
                        marginBottom: 8 
                      }}>
                        <Text strong>#{task.id} {task.title}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          状态: {task.status.displayName} | 负责人: {task.assignee?.nickname || '未分配'}
                        </Text>
                      </div>
                    ))}
                  </Card>
                </Col>
              )}
            </Row>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default RequirementKanbanPage
