
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import { useAuthStore } from './stores/authStore'
import LoginPage from './pages/LoginPage'
import RegisterPage from './pages/RegisterPage'
import DashboardLayout from './components/layout/DashboardLayout'
import DashboardPage from './pages/DashboardPage'
import TasksPage from './pages/TasksPage'
import TaskDetailPage from './pages/TaskDetailPage'
import RequirementsPage from './pages/RequirementsPage'
import RequirementKanbanPage from './pages/RequirementKanbanPage'
import QuadrantPage from './pages/QuadrantPage'
import WeeklyPlanPage from './pages/WeeklyPlanPage'
import UserManagePage from './pages/UserManagePage'
import ProfilePage from './pages/ProfilePage'

const { Content } = Layout

function App() {
  const { token } = useAuthStore()

  // 如果未登录，显示登录相关页面
  if (!token) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <Content>
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </Content>
      </Layout>
    )
  }

  // 已登录，显示主应用
  return (
    <DashboardLayout>
      <Routes>
        <Route path="/" element={<DashboardPage />} />
        <Route path="/tasks" element={<TasksPage />} />
        <Route path="/tasks/:id" element={<TaskDetailPage />} />
        <Route path="/requirements" element={<RequirementsPage />} />
        <Route path="/requirement-kanban" element={<RequirementKanbanPage />} />
        <Route path="/quadrant" element={<QuadrantPage />} />
        <Route path="/weekly-plan" element={<WeeklyPlanPage />} />
        <Route path="/users" element={<UserManagePage />} />
        <Route path="/profile" element={<ProfilePage />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </DashboardLayout>
  )
}

export default App
