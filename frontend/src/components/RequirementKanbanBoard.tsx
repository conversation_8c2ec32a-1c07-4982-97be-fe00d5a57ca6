import React from 'react'
import { Card, Col, Row, Badge, Empty, Spin, Typography } from 'antd'
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd'
import type { RequirementKanbanData, Requirement, RequirementStatus } from '../types'
import RequirementCard from './RequirementCard'

const { Text } = Typography

interface RequirementKanbanBoardProps {
  data: RequirementKanbanData | undefined
  loading?: boolean
  onRequirementClick?: (requirement: Requirement) => void
  onRequirementMove?: (requirementId: number, newStatus: RequirementStatus) => void
}

const RequirementKanbanBoard: React.FC<RequirementKanbanBoardProps> = ({ 
  data, 
  loading, 
  onRequirementClick, 
  onRequirementMove 
}) => {
  const columns = [
    {
      id: 'draft',
      title: '草稿',
      status: 'DRAFT' as RequirementStatus,
      color: '#d9d9d9',
      description: '初步想法和概念',
      requirements: data?.draft || []
    },
    {
      id: 'review',
      title: '评审中',
      status: 'REVIEW' as RequirementStatus,
      color: '#1890ff',
      description: '等待评审和讨论',
      requirements: data?.review || []
    },
    {
      id: 'approved',
      title: '已批准',
      status: 'APPROVED' as RequirementStatus,
      color: '#52c41a',
      description: '已通过评审，待开发',
      requirements: data?.approved || []
    },
    {
      id: 'inProgress',
      title: '进行中',
      status: 'IN_PROGRESS' as RequirementStatus,
      color: '#faad14',
      description: '正在开发实现',
      requirements: data?.inProgress || []
    },
    {
      id: 'testing',
      title: '测试中',
      status: 'TESTING' as RequirementStatus,
      color: '#722ed1',
      description: '功能测试和验收',
      requirements: data?.testing || []
    },
    {
      id: 'delivered',
      title: '已交付',
      status: 'DELIVERED' as RequirementStatus,
      color: '#13c2c2',
      description: '已完成并交付',
      requirements: data?.delivered || []
    }
  ]

  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result

    // 如果没有目标位置，或者位置没有变化，则不处理
    if (!destination || 
        (destination.droppableId === source.droppableId && 
         destination.index === source.index)) {
      return
    }

    // 找到目标列的状态
    const targetColumn = columns.find(col => col.id === destination.droppableId)
    if (!targetColumn) return

    // 调用回调函数更新需求状态
    const requirementId = parseInt(draggableId)
    onRequirementMove?.(requirementId, targetColumn.status)
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: 50 }}>
        <Spin size="large" />
      </div>
    )
  }

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Row gutter={[16, 16]}>
        {columns.map((column) => (
          <Col key={column.id} xs={24} sm={12} md={8} lg={4} xl={4} style={{ marginBottom: 16 }}>
            <Card
              title={
                <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <div
                      style={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        backgroundColor: column.color
                      }}
                    />
                    <span style={{ fontSize: 14, fontWeight: 600 }}>{column.title}</span>
                    <Badge 
                      count={column.requirements.length} 
                      style={{ backgroundColor: column.color }}
                    />
                  </div>
                  <Text 
                    type="secondary" 
                    style={{ fontSize: 11, lineHeight: '14px' }}
                  >
                    {column.description}
                  </Text>
                </div>
              }
              size="small"
              bodyStyle={{
                padding: 8,
                minHeight: window.innerWidth < 768 ? 300 : 500,
                maxHeight: window.innerWidth < 768 ? 400 : 700,
                overflow: 'auto'
              }}
              headStyle={{
                padding: '8px 12px',
                minHeight: 'auto'
              }}
            >
              <Droppable droppableId={column.id}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    style={{
                      minHeight: 400,
                      backgroundColor: snapshot.isDraggingOver ? '#f0f0f0' : 'transparent',
                      borderRadius: 4,
                      padding: 4,
                      transition: 'background-color 0.2s ease'
                    }}
                  >
                    {column.requirements.length === 0 ? (
                      <Empty 
                        image={Empty.PRESENTED_IMAGE_SIMPLE} 
                        description={
                          <span style={{ fontSize: 12, color: '#999' }}>
                            暂无需求
                          </span>
                        }
                        style={{ margin: '40px 0' }}
                      />
                    ) : (
                      column.requirements.map((requirement, index) => (
                        <Draggable
                          key={requirement.id}
                          draggableId={requirement.id.toString()}
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={{
                                ...provided.draggableProps.style,
                                transform: snapshot.isDragging 
                                  ? provided.draggableProps.style?.transform 
                                  : 'none',
                                opacity: snapshot.isDragging ? 0.8 : 1,
                                position: 'relative'
                              }}
                            >
                              <RequirementCard
                                requirement={requirement}
                                onClick={() => onRequirementClick?.(requirement)}
                              />
                            </div>
                          )}
                        </Draggable>
                      ))
                    )}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </Card>
          </Col>
        ))}
      </Row>
    </DragDropContext>
  )
}

export default RequirementKanbanBoard
